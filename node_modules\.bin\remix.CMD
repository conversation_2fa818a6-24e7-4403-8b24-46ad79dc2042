@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=J:\Projects\EButler\enable\plugins\enable-loyalty\node_modules\.pnpm\@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06\node_modules\@remix-run\dev\dist\node_modules;J:\Projects\EButler\enable\plugins\enable-loyalty\node_modules\.pnpm\@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06\node_modules\@remix-run\dev\node_modules;J:\Projects\EButler\enable\plugins\enable-loyalty\node_modules\.pnpm\@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06\node_modules\@remix-run\node_modules;J:\Projects\EButler\enable\plugins\enable-loyalty\node_modules\.pnpm\@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06\node_modules;J:\Projects\EButler\enable\plugins\enable-loyalty\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=J:\Projects\EButler\enable\plugins\enable-loyalty\node_modules\.pnpm\@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06\node_modules\@remix-run\dev\dist\node_modules;J:\Projects\EButler\enable\plugins\enable-loyalty\node_modules\.pnpm\@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06\node_modules\@remix-run\dev\node_modules;J:\Projects\EButler\enable\plugins\enable-loyalty\node_modules\.pnpm\@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06\node_modules\@remix-run\node_modules;J:\Projects\EButler\enable\plugins\enable-loyalty\node_modules\.pnpm\@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06\node_modules;J:\Projects\EButler\enable\plugins\enable-loyalty\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06\node_modules\@remix-run\dev\dist\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06\node_modules\@remix-run\dev\dist\cli.js" %*
)
