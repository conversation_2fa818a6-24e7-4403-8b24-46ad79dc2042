hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@ardatan/relay-compiler@12.0.3(graphql@16.11.0)':
    '@ardatan/relay-compiler': private
  '@ardatan/sync-fetch@0.0.1':
    '@ardatan/sync-fetch': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.0':
    '@babel/core': private
  '@babel/eslint-parser@7.28.0(@babel/core@7.28.0)(eslint@8.57.1)':
    '@babel/eslint-parser': private
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.27.3':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-member-expression-to-functions@7.27.1':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.27.1':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/plugin-syntax-decorators@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-decorators': private
  '@babel/plugin-syntax-import-assertions@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-assertions': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-typescript': private
  '@babel/plugin-transform-modules-commonjs@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-modules-commonjs': private
  '@babel/plugin-transform-react-display-name@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-display-name': private
  '@babel/plugin-transform-react-jsx-development@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-development': private
  '@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx': private
  '@babel/plugin-transform-react-pure-annotations@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-pure-annotations': private
  '@babel/plugin-transform-typescript@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-typescript': private
  '@babel/preset-react@7.27.1(@babel/core@7.28.0)':
    '@babel/preset-react': private
  '@babel/preset-typescript@7.27.1(@babel/core@7.28.0)':
    '@babel/preset-typescript': private
  '@babel/runtime@7.27.6':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
  '@babel/types@7.28.0':
    '@babel/types': private
  '@emotion/hash@0.9.2':
    '@emotion/hash': private
  '@envelop/core@5.3.0':
    '@envelop/core': private
  '@envelop/instrumentation@1.0.0':
    '@envelop/instrumentation': private
  '@envelop/types@5.2.1':
    '@envelop/types': private
  '@esbuild/aix-ppc64@0.25.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.17.6':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.17.6':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.17.6':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.17.6':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.17.6':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.17.6':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.17.6':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.17.6':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.17.6':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.17.6':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.17.6':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.17.6':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.17.6':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.17.6':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.17.6':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.17.6':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.5':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.17.6':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.5':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.17.6':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.17.6':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.17.6':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.17.6':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.17.6':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.1)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': private
  '@eslint/js@8.57.1':
    '@eslint/js': private
  '@fastify/busboy@3.1.1':
    '@fastify/busboy': private
  '@graphql-codegen/add@5.0.3(graphql@16.11.0)':
    '@graphql-codegen/add': private
  '@graphql-codegen/cli@5.0.7(@parcel/watcher@2.5.1)(@types/node@22.16.0)(graphql@16.11.0)(typescript@5.8.3)':
    '@graphql-codegen/cli': private
  '@graphql-codegen/client-preset@4.7.0(graphql@16.11.0)':
    '@graphql-codegen/client-preset': private
  '@graphql-codegen/core@4.0.2(graphql@16.11.0)':
    '@graphql-codegen/core': private
  '@graphql-codegen/gql-tag-operations@4.0.16(graphql@16.11.0)':
    '@graphql-codegen/gql-tag-operations': private
  '@graphql-codegen/introspection@4.0.3(graphql@16.11.0)':
    '@graphql-codegen/introspection': private
  '@graphql-codegen/plugin-helpers@5.1.1(graphql@16.11.0)':
    '@graphql-codegen/plugin-helpers': private
  '@graphql-codegen/schema-ast@4.1.0(graphql@16.11.0)':
    '@graphql-codegen/schema-ast': private
  '@graphql-codegen/typed-document-node@5.1.2(graphql@16.11.0)':
    '@graphql-codegen/typed-document-node': private
  '@graphql-codegen/typescript-operations@4.5.0(graphql@16.11.0)':
    '@graphql-codegen/typescript-operations': private
  '@graphql-codegen/typescript@4.1.6(graphql@16.11.0)':
    '@graphql-codegen/typescript': private
  '@graphql-codegen/visitor-plugin-common@5.8.0(graphql@16.11.0)':
    '@graphql-codegen/visitor-plugin-common': private
  '@graphql-hive/signal@1.0.0':
    '@graphql-hive/signal': private
  '@graphql-tools/apollo-engine-loader@8.0.20(graphql@16.11.0)':
    '@graphql-tools/apollo-engine-loader': private
  '@graphql-tools/batch-execute@9.0.17(graphql@16.11.0)':
    '@graphql-tools/batch-execute': private
  '@graphql-tools/code-file-loader@8.1.20(graphql@16.11.0)':
    '@graphql-tools/code-file-loader': private
  '@graphql-tools/delegate@10.2.20(graphql@16.11.0)':
    '@graphql-tools/delegate': private
  '@graphql-tools/documents@1.0.1(graphql@16.11.0)':
    '@graphql-tools/documents': private
  '@graphql-tools/executor-common@0.0.1(graphql@16.11.0)':
    '@graphql-tools/executor-common': private
  '@graphql-tools/executor-graphql-ws@1.3.7(graphql@16.11.0)':
    '@graphql-tools/executor-graphql-ws': private
  '@graphql-tools/executor-http@1.3.3(@types/node@22.16.0)(graphql@16.11.0)':
    '@graphql-tools/executor-http': private
  '@graphql-tools/executor-legacy-ws@1.1.17(graphql@16.11.0)':
    '@graphql-tools/executor-legacy-ws': private
  '@graphql-tools/executor@1.4.7(graphql@16.11.0)':
    '@graphql-tools/executor': private
  '@graphql-tools/git-loader@8.0.24(graphql@16.11.0)':
    '@graphql-tools/git-loader': private
  '@graphql-tools/github-loader@8.0.20(@types/node@22.16.0)(graphql@16.11.0)':
    '@graphql-tools/github-loader': private
  '@graphql-tools/graphql-file-loader@8.0.20(graphql@16.11.0)':
    '@graphql-tools/graphql-file-loader': private
  '@graphql-tools/graphql-tag-pluck@8.3.19(graphql@16.11.0)':
    '@graphql-tools/graphql-tag-pluck': private
  '@graphql-tools/import@7.0.19(graphql@16.11.0)':
    '@graphql-tools/import': private
  '@graphql-tools/json-file-loader@8.0.18(graphql@16.11.0)':
    '@graphql-tools/json-file-loader': private
  '@graphql-tools/load@8.1.0(graphql@16.11.0)':
    '@graphql-tools/load': private
  '@graphql-tools/merge@9.0.24(graphql@16.11.0)':
    '@graphql-tools/merge': private
  '@graphql-tools/optimize@2.0.0(graphql@16.11.0)':
    '@graphql-tools/optimize': private
  '@graphql-tools/prisma-loader@8.0.17(@types/node@22.16.0)(graphql@16.11.0)':
    '@graphql-tools/prisma-loader': private
  '@graphql-tools/relay-operation-optimizer@7.0.19(graphql@16.11.0)':
    '@graphql-tools/relay-operation-optimizer': private
  '@graphql-tools/schema@10.0.23(graphql@16.11.0)':
    '@graphql-tools/schema': private
  '@graphql-tools/url-loader@8.0.16(@types/node@22.16.0)(graphql@16.11.0)':
    '@graphql-tools/url-loader': private
  '@graphql-tools/utils@10.8.6(graphql@16.11.0)':
    '@graphql-tools/utils': private
  '@graphql-tools/wrap@10.1.1(graphql@16.11.0)':
    '@graphql-tools/wrap': private
  '@graphql-typed-document-node/core@3.2.0(graphql@16.11.0)':
    '@graphql-typed-document-node/core': private
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@jspm/core@2.1.0':
    '@jspm/core': private
  '@mdx-js/mdx@2.3.0':
    '@mdx-js/mdx': private
  '@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1':
    '@nicolo-ribaudo/eslint-scope-5-internals': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nolyfill/is-core-module@1.0.39':
    '@nolyfill/is-core-module': private
  '@npmcli/fs@3.1.1':
    '@npmcli/fs': private
  '@npmcli/git@4.1.0':
    '@npmcli/git': private
  '@npmcli/package-json@4.0.1':
    '@npmcli/package-json': private
  '@npmcli/promise-spawn@6.0.2':
    '@npmcli/promise-spawn': private
  '@parcel/watcher-android-arm64@2.5.1':
    '@parcel/watcher-android-arm64': private
  '@parcel/watcher-darwin-arm64@2.5.1':
    '@parcel/watcher-darwin-arm64': private
  '@parcel/watcher-darwin-x64@2.5.1':
    '@parcel/watcher-darwin-x64': private
  '@parcel/watcher-freebsd-x64@2.5.1':
    '@parcel/watcher-freebsd-x64': private
  '@parcel/watcher-linux-arm-glibc@2.5.1':
    '@parcel/watcher-linux-arm-glibc': private
  '@parcel/watcher-linux-arm-musl@2.5.1':
    '@parcel/watcher-linux-arm-musl': private
  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    '@parcel/watcher-linux-arm64-glibc': private
  '@parcel/watcher-linux-arm64-musl@2.5.1':
    '@parcel/watcher-linux-arm64-musl': private
  '@parcel/watcher-linux-x64-glibc@2.5.1':
    '@parcel/watcher-linux-x64-glibc': private
  '@parcel/watcher-linux-x64-musl@2.5.1':
    '@parcel/watcher-linux-x64-musl': private
  '@parcel/watcher-win32-arm64@2.5.1':
    '@parcel/watcher-win32-arm64': private
  '@parcel/watcher-win32-ia32@2.5.1':
    '@parcel/watcher-win32-ia32': private
  '@parcel/watcher-win32-x64@2.5.1':
    '@parcel/watcher-win32-x64': private
  '@parcel/watcher@2.5.1':
    '@parcel/watcher': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@prisma/config@6.11.1':
    '@prisma/config': private
  '@prisma/debug@6.11.1':
    '@prisma/debug': private
  '@prisma/engines-version@6.11.1-1.f40f79ec31188888a2e33acda0ecc8fd10a853a9':
    '@prisma/engines-version': private
  '@prisma/engines@6.11.1':
    '@prisma/engines': private
  '@prisma/fetch-engine@6.11.1':
    '@prisma/fetch-engine': private
  '@prisma/get-platform@6.11.1':
    '@prisma/get-platform': private
  '@remix-run/express@2.16.8(express@4.21.2)(typescript@5.8.3)':
    '@remix-run/express': private
  '@remix-run/router@1.23.0':
    '@remix-run/router': private
  '@remix-run/server-runtime@2.16.8(typescript@5.8.3)':
    '@remix-run/server-runtime': private
  '@remix-run/web-blob@3.1.0':
    '@remix-run/web-blob': private
  '@remix-run/web-fetch@4.4.2':
    '@remix-run/web-fetch': private
  '@remix-run/web-file@3.1.0':
    '@remix-run/web-file': private
  '@remix-run/web-form-data@3.1.0':
    '@remix-run/web-form-data': private
  '@remix-run/web-stream@1.1.0':
    '@remix-run/web-stream': private
  '@repeaterjs/repeater@3.0.6':
    '@repeaterjs/repeater': private
  '@rollup/rollup-android-arm-eabi@4.44.2':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.44.2':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.44.2':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.44.2':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.44.2':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.44.2':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.44.2':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.44.2':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.44.2':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.44.2':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.44.2':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.44.2':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.44.2':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.44.2':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.44.2':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.44.2':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.44.2':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.44.2':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.44.2':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.44.2':
    '@rollup/rollup-win32-x64-msvc': private
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': private
  '@rushstack/eslint-patch@1.12.0':
    '@rushstack/eslint-patch': private
  '@shopify/admin-api-client@1.1.0':
    '@shopify/admin-api-client': private
  '@shopify/app-bridge-types@0.1.0':
    '@shopify/app-bridge-types': private
  '@shopify/graphql-client@1.4.0':
    '@shopify/graphql-client': private
  '@shopify/graphql-codegen@0.1.0(graphql@16.11.0)':
    '@shopify/graphql-codegen': private
  '@shopify/network@3.3.0':
    '@shopify/network': private
  '@shopify/polaris-icons@8.11.1(react@18.3.1)':
    '@shopify/polaris-icons': private
  '@shopify/polaris-tokens@8.10.0':
    '@shopify/polaris-tokens': private
  '@shopify/polaris@12.27.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@shopify/polaris': private
  '@shopify/shopify-api@11.13.0':
    '@shopify/shopify-api': private
  '@shopify/shopify-app-session-storage@3.0.18(@shopify/shopify-api@11.13.0)':
    '@shopify/shopify-app-session-storage': private
  '@shopify/storefront-api-client@1.0.8':
    '@shopify/storefront-api-client': private
  '@tailwindcss/node@4.1.11':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-android-arm64@4.1.11':
    '@tailwindcss/oxide-android-arm64': private
  '@tailwindcss/oxide-darwin-arm64@4.1.11':
    '@tailwindcss/oxide-darwin-arm64': private
  '@tailwindcss/oxide-darwin-x64@4.1.11':
    '@tailwindcss/oxide-darwin-x64': private
  '@tailwindcss/oxide-freebsd-x64@4.1.11':
    '@tailwindcss/oxide-freebsd-x64': private
  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11':
    '@tailwindcss/oxide-linux-arm-gnueabihf': private
  '@tailwindcss/oxide-linux-arm64-gnu@4.1.11':
    '@tailwindcss/oxide-linux-arm64-gnu': private
  '@tailwindcss/oxide-linux-arm64-musl@4.1.11':
    '@tailwindcss/oxide-linux-arm64-musl': private
  '@tailwindcss/oxide-linux-x64-gnu@4.1.11':
    '@tailwindcss/oxide-linux-x64-gnu': private
  '@tailwindcss/oxide-linux-x64-musl@4.1.11':
    '@tailwindcss/oxide-linux-x64-musl': private
  '@tailwindcss/oxide-wasm32-wasi@4.1.11':
    '@tailwindcss/oxide-wasm32-wasi': private
  '@tailwindcss/oxide-win32-arm64-msvc@4.1.11':
    '@tailwindcss/oxide-win32-arm64-msvc': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.11':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.11':
    '@tailwindcss/oxide': private
  '@testing-library/dom@8.20.1':
    '@testing-library/dom': private
  '@types/acorn@4.0.6':
    '@types/acorn': private
  '@types/aria-query@5.0.4':
    '@types/aria-query': private
  '@types/cookie@0.6.0':
    '@types/cookie': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/estree-jsx@1.0.5':
    '@types/estree-jsx': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/hast@2.3.10':
    '@types/hast': private
  '@types/js-yaml@4.0.9':
    '@types/js-yaml': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/mdast@3.0.15':
    '@types/mdast': private
  '@types/mdx@2.0.13':
    '@types/mdx': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/prop-types@15.7.15':
    '@types/prop-types': private
  '@types/react-transition-group@4.4.12(@types/react@18.3.23)':
    '@types/react-transition-group': private
  '@types/semver@7.7.0':
    '@types/semver': private
  '@types/unist@2.0.11':
    '@types/unist': private
  '@types/ws@8.18.1':
    '@types/ws': private
  '@typescript-eslint/eslint-plugin@5.62.0(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/scope-manager@5.62.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@5.62.0(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@5.62.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@5.62.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@5.62.0(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@5.62.0':
    '@typescript-eslint/visitor-keys': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@unrs/resolver-binding-android-arm-eabi@1.10.1':
    '@unrs/resolver-binding-android-arm-eabi': private
  '@unrs/resolver-binding-android-arm64@1.10.1':
    '@unrs/resolver-binding-android-arm64': private
  '@unrs/resolver-binding-darwin-arm64@1.10.1':
    '@unrs/resolver-binding-darwin-arm64': private
  '@unrs/resolver-binding-darwin-x64@1.10.1':
    '@unrs/resolver-binding-darwin-x64': private
  '@unrs/resolver-binding-freebsd-x64@1.10.1':
    '@unrs/resolver-binding-freebsd-x64': private
  '@unrs/resolver-binding-linux-arm-gnueabihf@1.10.1':
    '@unrs/resolver-binding-linux-arm-gnueabihf': private
  '@unrs/resolver-binding-linux-arm-musleabihf@1.10.1':
    '@unrs/resolver-binding-linux-arm-musleabihf': private
  '@unrs/resolver-binding-linux-arm64-gnu@1.10.1':
    '@unrs/resolver-binding-linux-arm64-gnu': private
  '@unrs/resolver-binding-linux-arm64-musl@1.10.1':
    '@unrs/resolver-binding-linux-arm64-musl': private
  '@unrs/resolver-binding-linux-ppc64-gnu@1.10.1':
    '@unrs/resolver-binding-linux-ppc64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-gnu@1.10.1':
    '@unrs/resolver-binding-linux-riscv64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-musl@1.10.1':
    '@unrs/resolver-binding-linux-riscv64-musl': private
  '@unrs/resolver-binding-linux-s390x-gnu@1.10.1':
    '@unrs/resolver-binding-linux-s390x-gnu': private
  '@unrs/resolver-binding-linux-x64-gnu@1.10.1':
    '@unrs/resolver-binding-linux-x64-gnu': private
  '@unrs/resolver-binding-linux-x64-musl@1.10.1':
    '@unrs/resolver-binding-linux-x64-musl': private
  '@unrs/resolver-binding-wasm32-wasi@1.10.1':
    '@unrs/resolver-binding-wasm32-wasi': private
  '@unrs/resolver-binding-win32-arm64-msvc@1.10.1':
    '@unrs/resolver-binding-win32-arm64-msvc': private
  '@unrs/resolver-binding-win32-ia32-msvc@1.10.1':
    '@unrs/resolver-binding-win32-ia32-msvc': private
  '@unrs/resolver-binding-win32-x64-msvc@1.10.1':
    '@unrs/resolver-binding-win32-x64-msvc': private
  '@vanilla-extract/babel-plugin-debug-ids@1.2.2':
    '@vanilla-extract/babel-plugin-debug-ids': private
  '@vanilla-extract/css@1.17.4':
    '@vanilla-extract/css': private
  '@vanilla-extract/integration@6.5.0(@types/node@22.16.0)(lightningcss@1.30.1)':
    '@vanilla-extract/integration': private
  '@vanilla-extract/private@1.0.9':
    '@vanilla-extract/private': private
  '@web3-storage/multipart-parser@1.0.0':
    '@web3-storage/multipart-parser': private
  '@whatwg-node/disposablestack@0.0.5':
    '@whatwg-node/disposablestack': private
  '@whatwg-node/fetch@0.10.8':
    '@whatwg-node/fetch': private
  '@whatwg-node/node-fetch@0.7.21':
    '@whatwg-node/node-fetch': private
  '@whatwg-node/promise-helpers@1.3.2':
    '@whatwg-node/promise-helpers': private
  '@zxing/text-encoding@0.9.0':
    '@zxing/text-encoding': private
  abort-controller@3.0.0:
    abort-controller: private
  accepts@1.3.8:
    accepts: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  agent-base@7.1.3:
    agent-base: private
  aggregate-error@3.1.0:
    aggregate-error: private
  ajv@6.12.6:
    ajv: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-query@5.3.2:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-flatten@1.1.1:
    array-flatten: private
  array-includes@3.1.9:
    array-includes: private
  array-union@2.1.0:
    array-union: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.6:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  asap@2.0.6:
    asap: private
  ast-types-flow@0.0.8:
    ast-types-flow: private
  astral-regex@2.0.0:
    astral-regex: private
  astring@1.9.0:
    astring: private
  async-function@1.0.0:
    async-function: private
  auto-bind@4.0.0:
    auto-bind: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axe-core@4.10.3:
    axe-core: private
  axobject-query@4.1.0:
    axobject-query: private
  bail@2.0.2:
    bail: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  basic-auth@2.0.1:
    basic-auth: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bl@4.1.0:
    bl: private
  body-parser@1.20.3:
    body-parser: private
  brace-expansion@2.0.2:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserify-zlib@0.1.4:
    browserify-zlib: private
  browserslist@4.25.1:
    browserslist: private
  bser@2.1.1:
    bser: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@5.7.1:
    buffer: private
  bytes@3.1.2:
    bytes: private
  cac@6.7.14:
    cac: private
  cacache@17.1.4:
    cacache: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camel-case@4.1.2:
    camel-case: private
  caniuse-lite@1.0.30001726:
    caniuse-lite: private
  capital-case@1.0.4:
    capital-case: private
  ccount@2.0.1:
    ccount: private
  chalk@4.1.2:
    chalk: private
  change-case-all@1.0.15:
    change-case-all: private
  change-case@4.1.2:
    change-case: private
  character-entities-html4@2.1.0:
    character-entities-html4: private
  character-entities-legacy@3.0.0:
    character-entities-legacy: private
  character-entities@2.0.2:
    character-entities: private
  character-reference-invalid@2.0.1:
    character-reference-invalid: private
  chardet@0.7.0:
    chardet: private
  chokidar@3.6.0:
    chokidar: private
  chownr@1.1.4:
    chownr: private
  clean-stack@2.2.0:
    clean-stack: private
  cli-cursor@3.1.0:
    cli-cursor: private
  cli-spinners@2.9.2:
    cli-spinners: private
  cli-truncate@2.1.0:
    cli-truncate: private
  cli-width@3.0.0:
    cli-width: private
  cliui@8.0.1:
    cliui: private
  clone@1.0.4:
    clone: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  colorette@2.0.20:
    colorette: private
  comma-separated-tokens@2.0.3:
    comma-separated-tokens: private
  common-tags@1.8.2:
    common-tags: private
  compare-versions@6.1.1:
    compare-versions: private
  compressible@2.0.18:
    compressible: private
  compression@1.8.0:
    compression: private
  confbox@0.1.8:
    confbox: private
  constant-case@3.0.4:
    constant-case: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie-signature@1.2.2:
    cookie-signature: private
  cookie@0.7.2:
    cookie: private
  core-util-is@1.0.3:
    core-util-is: private
  cosmiconfig@8.3.6(typescript@5.8.3):
    cosmiconfig: private
  cross-fetch@3.2.0:
    cross-fetch: private
  cross-inspect@1.0.1:
    cross-inspect: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-what@6.2.2:
    css-what: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: private
  data-uri-to-buffer@3.0.1:
    data-uri-to-buffer: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  dataloader@2.2.3:
    dataloader: private
  debounce@1.2.1:
    debounce: private
  debug@4.4.1:
    debug: private
  decode-named-character-reference@1.2.0:
    decode-named-character-reference: private
  dedent@1.6.0:
    dedent: private
  deep-equal@2.2.3:
    deep-equal: private
  deep-is@0.1.4:
    deep-is: private
  deep-object-diff@1.1.9:
    deep-object-diff: private
  deepmerge@4.3.1:
    deepmerge: private
  defaults@1.0.4:
    defaults: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  depd@2.0.0:
    depd: private
  dependency-graph@0.11.0:
    dependency-graph: private
  dequal@2.0.3:
    dequal: private
  destroy@1.2.0:
    destroy: private
  detect-indent@6.1.0:
    detect-indent: private
  detect-libc@1.0.3:
    detect-libc: private
  diff@5.2.0:
    diff: private
  dir-glob@3.0.1:
    dir-glob: private
  doctrine@3.0.0:
    doctrine: private
  dom-accessibility-api@0.5.16:
    dom-accessibility-api: private
  dom-helpers@5.2.1:
    dom-helpers: private
  dot-case@3.0.4:
    dot-case: private
  dotenv@16.6.1:
    dotenv: private
  dset@3.1.4:
    dset: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexify@3.7.1:
    duplexify: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  ee-first@1.1.1:
    ee-first: private
  electron-to-chromium@1.5.179:
    electron-to-chromium: private
  emoji-regex@9.2.2:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  end-of-stream@1.4.5:
    end-of-stream: private
  enhanced-resolve@5.18.2:
    enhanced-resolve: private
  err-code@2.0.3:
    err-code: private
  error-ex@1.3.2:
    error-ex: private
  es-abstract@1.24.0:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-get-iterator@1.1.3:
    es-get-iterator: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  esbuild-plugins-node-modules-polyfill@1.7.1(esbuild@0.17.6):
    esbuild-plugins-node-modules-polyfill: private
  esbuild@0.17.6:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-import-resolver-node@0.3.7:
    eslint-import-resolver-node: private
  eslint-import-resolver-typescript@3.10.1(eslint-plugin-import@2.32.0)(eslint@8.57.1):
    eslint-import-resolver-typescript: private
  eslint-module-utils@2.12.1(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@5.8.3))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.10.1)(eslint@8.57.1):
    eslint-module-utils: private
  eslint-plugin-es@3.0.1(eslint@8.57.1):
    eslint-plugin-es: private
  eslint-plugin-import@2.32.0(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@5.8.3))(eslint-import-resolver-typescript@3.10.1)(eslint@8.57.1):
    eslint-plugin-import: private
  eslint-plugin-jest-dom@4.0.3(eslint@8.57.1):
    eslint-plugin-jest-dom: private
  eslint-plugin-jest@26.9.0(@typescript-eslint/eslint-plugin@5.62.0(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1)(typescript@5.8.3):
    eslint-plugin-jest: private
  eslint-plugin-jsx-a11y@6.10.2(eslint@8.57.1):
    eslint-plugin-jsx-a11y: private
  eslint-plugin-node@11.1.0(eslint@8.57.1):
    eslint-plugin-node: private
  eslint-plugin-react-hooks@4.6.2(eslint@8.57.1):
    eslint-plugin-react-hooks: private
  eslint-plugin-react@7.37.5(eslint@8.57.1):
    eslint-plugin-react: private
  eslint-plugin-testing-library@5.11.1(eslint@8.57.1)(typescript@5.8.3):
    eslint-plugin-testing-library: private
  eslint-scope@7.2.2:
    eslint-scope: private
  eslint-utils@2.1.0:
    eslint-utils: private
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: private
  espree@9.6.1:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-util-attach-comments@2.1.1:
    estree-util-attach-comments: private
  estree-util-build-jsx@2.2.2:
    estree-util-build-jsx: private
  estree-util-is-identifier-name@2.1.0:
    estree-util-is-identifier-name: private
  estree-util-to-js@1.2.0:
    estree-util-to-js: private
  estree-util-value-to-estree@1.3.0:
    estree-util-value-to-estree: private
  estree-util-visit@1.2.1:
    estree-util-visit: private
  estree-walker@3.0.3:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  eval@0.1.8:
    eval: private
  event-target-shim@5.0.1:
    event-target-shim: private
  execa@5.1.1:
    execa: private
  exit-hook@2.2.1:
    exit-hook: private
  express@4.21.2:
    express: private
  exsolve@1.0.7:
    exsolve: private
  extend@3.0.2:
    extend: private
  external-editor@3.1.0:
    external-editor: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fault@2.0.1:
    fault: private
  fb-watchman@2.0.2:
    fb-watchman: private
  fbjs-css-vars@1.0.2:
    fbjs-css-vars: private
  fbjs@3.0.5:
    fbjs: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  fetch-blob@3.2.0:
    fetch-blob: private
  figures@3.2.0:
    figures: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.3.1:
    finalhandler: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  format@0.2.2:
    format: private
  formdata-polyfill@4.0.10:
    formdata-polyfill: private
  forwarded@0.2.0:
    forwarded: private
  fraction.js@4.3.7:
    fraction.js: private
  fresh@0.5.2:
    fresh: private
  fs-constants@1.0.0:
    fs-constants: private
  fs-extra@10.1.0:
    fs-extra: private
  fs-minipass@3.0.3:
    fs-minipass: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  generic-names@4.0.0:
    generic-names: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-port@5.1.1:
    get-port: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@6.0.1:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@10.4.5:
    glob: private
  globals@13.24.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@11.1.0:
    globby: private
  globrex@0.1.2:
    globrex: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  graphql-config@5.1.5(@types/node@22.16.0)(graphql@16.11.0)(typescript@5.8.3):
    graphql-config: private
  graphql-request@6.1.0(graphql@16.11.0):
    graphql-request: private
  graphql-tag@2.12.6(graphql@16.11.0):
    graphql-tag: private
  graphql-ws@5.16.2(graphql@16.11.0):
    graphql-ws: private
  graphql@16.11.0:
    graphql: private
  gunzip-maybe@1.4.2:
    gunzip-maybe: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  hast-util-to-estree@2.3.3:
    hast-util-to-estree: private
  hast-util-whitespace@2.0.1:
    hast-util-whitespace: private
  header-case@2.0.4:
    header-case: private
  hosted-git-info@6.1.3:
    hosted-git-info: private
  http-errors@2.0.0:
    http-errors: private
  http-proxy-agent@7.0.2:
    http-proxy-agent: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  human-signals@2.1.0:
    human-signals: private
  iconv-lite@0.4.24:
    iconv-lite: private
  icss-utils@5.1.0(postcss@8.5.6):
    icss-utils: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  immutable@3.7.6:
    immutable: private
  import-fresh@3.3.1:
    import-fresh: private
  import-from@4.0.0:
    import-from: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@4.0.0:
    indent-string: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  inline-style-parser@0.1.1:
    inline-style-parser: private
  inquirer@8.2.6:
    inquirer: private
  internal-slot@1.1.0:
    internal-slot: private
  invariant@2.2.4:
    invariant: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-absolute@1.0.0:
    is-absolute: private
  is-alphabetical@2.0.1:
    is-alphabetical: private
  is-alphanumerical@2.0.1:
    is-alphanumerical: private
  is-arguments@1.2.0:
    is-arguments: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-buffer@2.0.5:
    is-buffer: private
  is-bun-module@2.0.0:
    is-bun-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-decimal@2.0.1:
    is-decimal: private
  is-deflate@1.0.0:
    is-deflate: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-gzip@1.0.0:
    is-gzip: private
  is-hexadecimal@2.0.1:
    is-hexadecimal: private
  is-interactive@1.0.0:
    is-interactive: private
  is-lower-case@2.0.2:
    is-lower-case: private
  is-map@2.0.3:
    is-map: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-plain-obj@3.0.0:
    is-plain-obj: private
  is-reference@3.0.3:
    is-reference: private
  is-regex@1.2.1:
    is-regex: private
  is-relative@1.0.0:
    is-relative: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@2.0.1:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-unc-path@1.0.0:
    is-unc-path: private
  is-unicode-supported@0.1.0:
    is-unicode-supported: private
  is-upper-case@2.0.2:
    is-upper-case: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  is-windows@1.0.2:
    is-windows: private
  isarray@1.0.0:
    isarray: private
  isexe@2.0.0:
    isexe: private
  isomorphic-ws@5.0.0(ws@8.18.3):
    isomorphic-ws: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jackspeak@3.4.3:
    jackspeak: private
  javascript-stringify@2.1.0:
    javascript-stringify: private
  jiti@2.4.2:
    jiti: private
  jose@5.10.0:
    jose: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.0.2:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@3.0.2:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json-to-pretty-yaml@1.2.2:
    json-to-pretty-yaml: private
  json5@2.2.3:
    json5: private
  jsonfile@6.1.0:
    jsonfile: private
  jsonwebtoken@9.0.2:
    jsonwebtoken: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  jwa@1.4.2:
    jwa: private
  jws@3.2.2:
    jws: private
  keyv@4.5.4:
    keyv: private
  kleur@4.1.5:
    kleur: private
  language-subtag-registry@0.3.23:
    language-subtag-registry: private
  language-tags@1.0.9:
    language-tags: private
  levn@0.4.1:
    levn: private
  lightningcss-darwin-arm64@1.30.1:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.30.1:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.30.1:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.30.1:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.30.1:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.30.1:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.30.1:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.30.1:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.30.1:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  listr2@4.0.5:
    listr2: private
  loader-utils@3.3.1:
    loader-utils: private
  local-pkg@1.1.1:
    local-pkg: private
  locate-path@6.0.0:
    locate-path: private
  lodash.camelcase@4.3.0:
    lodash.camelcase: private
  lodash.debounce@4.0.8:
    lodash.debounce: private
  lodash.includes@4.3.0:
    lodash.includes: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isinteger@4.0.4:
    lodash.isinteger: private
  lodash.isnumber@3.0.3:
    lodash.isnumber: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.isstring@4.0.1:
    lodash.isstring: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.once@4.1.1:
    lodash.once: private
  lodash.sortby@4.7.0:
    lodash.sortby: private
  lodash@4.17.21:
    lodash: private
  log-symbols@4.1.0:
    log-symbols: private
  log-update@4.0.0:
    log-update: private
  longest-streak@3.1.0:
    longest-streak: private
  loose-envify@1.4.0:
    loose-envify: private
  lower-case-first@2.0.2:
    lower-case-first: private
  lower-case@2.0.2:
    lower-case: private
  lru-cache@7.18.3:
    lru-cache: private
  lz-string@1.5.0:
    lz-string: private
  magic-string@0.30.17:
    magic-string: private
  map-cache@0.2.2:
    map-cache: private
  markdown-extensions@1.1.1:
    markdown-extensions: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdast-util-definitions@5.1.2:
    mdast-util-definitions: private
  mdast-util-from-markdown@1.3.1:
    mdast-util-from-markdown: private
  mdast-util-frontmatter@1.0.1:
    mdast-util-frontmatter: private
  mdast-util-mdx-expression@1.3.2:
    mdast-util-mdx-expression: private
  mdast-util-mdx-jsx@2.1.4:
    mdast-util-mdx-jsx: private
  mdast-util-mdx@2.0.1:
    mdast-util-mdx: private
  mdast-util-mdxjs-esm@1.3.1:
    mdast-util-mdxjs-esm: private
  mdast-util-phrasing@3.0.1:
    mdast-util-phrasing: private
  mdast-util-to-hast@12.3.0:
    mdast-util-to-hast: private
  mdast-util-to-markdown@1.5.0:
    mdast-util-to-markdown: private
  mdast-util-to-string@3.2.0:
    mdast-util-to-string: private
  media-query-parser@2.0.2:
    media-query-parser: private
  media-typer@0.3.0:
    media-typer: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  meros@1.3.1(@types/node@22.16.0):
    meros: private
  methods@1.1.2:
    methods: private
  micromark-core-commonmark@1.1.0:
    micromark-core-commonmark: private
  micromark-extension-frontmatter@1.1.1:
    micromark-extension-frontmatter: private
  micromark-extension-mdx-expression@1.0.8:
    micromark-extension-mdx-expression: private
  micromark-extension-mdx-jsx@1.0.5:
    micromark-extension-mdx-jsx: private
  micromark-extension-mdx-md@1.0.1:
    micromark-extension-mdx-md: private
  micromark-extension-mdxjs-esm@1.0.5:
    micromark-extension-mdxjs-esm: private
  micromark-extension-mdxjs@1.0.1:
    micromark-extension-mdxjs: private
  micromark-factory-destination@1.1.0:
    micromark-factory-destination: private
  micromark-factory-label@1.1.0:
    micromark-factory-label: private
  micromark-factory-mdx-expression@1.0.9:
    micromark-factory-mdx-expression: private
  micromark-factory-space@1.1.0:
    micromark-factory-space: private
  micromark-factory-title@1.1.0:
    micromark-factory-title: private
  micromark-factory-whitespace@1.1.0:
    micromark-factory-whitespace: private
  micromark-util-character@1.2.0:
    micromark-util-character: private
  micromark-util-chunked@1.1.0:
    micromark-util-chunked: private
  micromark-util-classify-character@1.1.0:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@1.1.0:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@1.1.0:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@1.1.0:
    micromark-util-decode-string: private
  micromark-util-encode@1.1.0:
    micromark-util-encode: private
  micromark-util-events-to-acorn@1.2.3:
    micromark-util-events-to-acorn: private
  micromark-util-html-tag-name@1.2.0:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@1.1.0:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@1.1.0:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@1.2.0:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@1.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@1.1.0:
    micromark-util-symbol: private
  micromark-util-types@1.1.0:
    micromark-util-types: private
  micromark@3.2.0:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@1.6.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  minimatch@9.0.5:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass-collect@1.0.2:
    minipass-collect: private
  minipass-flush@1.0.5:
    minipass-flush: private
  minipass-pipeline@1.2.4:
    minipass-pipeline: private
  minipass@7.1.2:
    minipass: private
  minizlib@2.1.2:
    minizlib: private
  mkdirp-classic@0.5.3:
    mkdirp-classic: private
  mkdirp@1.0.4:
    mkdirp: private
  mlly@1.7.4:
    mlly: private
  modern-ahocorasick@1.1.0:
    modern-ahocorasick: private
  morgan@1.10.0:
    morgan: private
  mri@1.2.0:
    mri: private
  mrmime@1.0.1:
    mrmime: private
  ms@2.1.3:
    ms: private
  mute-stream@0.0.8:
    mute-stream: private
  nanoid@3.3.11:
    nanoid: private
  napi-postinstall@0.3.0:
    napi-postinstall: private
  natural-compare-lite@1.4.0:
    natural-compare-lite: private
  natural-compare@1.4.0:
    natural-compare: private
  negotiator@0.6.4:
    negotiator: private
  no-case@3.0.4:
    no-case: private
  node-addon-api@7.1.1:
    node-addon-api: private
  node-domexception@1.0.0:
    node-domexception: private
  node-fetch@2.7.0:
    node-fetch: private
  node-int64@0.4.0:
    node-int64: private
  node-releases@2.0.19:
    node-releases: private
  normalize-package-data@5.0.0:
    normalize-package-data: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  npm-install-checks@6.3.0:
    npm-install-checks: private
  npm-normalize-package-bin@3.0.1:
    npm-normalize-package-bin: private
  npm-package-arg@10.1.0:
    npm-package-arg: private
  npm-pick-manifest@8.0.2:
    npm-pick-manifest: private
  npm-run-path@4.0.1:
    npm-run-path: private
  nullthrows@1.1.1:
    nullthrows: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  object-is@1.1.6:
    object-is: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.1:
    object.values: private
  on-finished@2.4.1:
    on-finished: private
  on-headers@1.0.2:
    on-headers: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  optionator@0.9.4:
    optionator: private
  ora@5.4.1:
    ora: private
  os-tmpdir@1.0.2:
    os-tmpdir: private
  outdent@0.8.0:
    outdent: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-map@4.0.0:
    p-map: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  pako@0.2.9:
    pako: private
  param-case@3.0.4:
    param-case: private
  parent-module@1.0.1:
    parent-module: private
  parse-entities@4.0.2:
    parse-entities: private
  parse-filepath@1.0.2:
    parse-filepath: private
  parse-json@5.2.0:
    parse-json: private
  parse-ms@2.1.0:
    parse-ms: private
  parseurl@1.3.3:
    parseurl: private
  pascal-case@3.1.2:
    pascal-case: private
  path-case@3.0.4:
    path-case: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-root-regex@0.1.2:
    path-root-regex: private
  path-root@0.1.1:
    path-root: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@0.1.12:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  pathe@1.1.2:
    pathe: private
  peek-stream@1.1.3:
    peek-stream: private
  periscopic@3.1.0:
    periscopic: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pidtree@0.6.0:
    pidtree: private
  pkg-types@2.2.0:
    pkg-types: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-discard-duplicates@5.1.0(postcss@8.5.6):
    postcss-discard-duplicates: private
  postcss-load-config@4.0.2(postcss@8.5.6):
    postcss-load-config: private
  postcss-modules-extract-imports@3.1.0(postcss@8.5.6):
    postcss-modules-extract-imports: private
  postcss-modules-local-by-default@4.2.0(postcss@8.5.6):
    postcss-modules-local-by-default: private
  postcss-modules-scope@3.2.1(postcss@8.5.6):
    postcss-modules-scope: private
  postcss-modules-values@4.0.0(postcss@8.5.6):
    postcss-modules-values: private
  postcss-modules@6.0.1(postcss@8.5.6):
    postcss-modules: private
  postcss-selector-parser@7.1.0:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postcss@8.5.6:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  pretty-format@27.5.1:
    pretty-format: private
  pretty-ms@7.0.1:
    pretty-ms: private
  proc-log@3.0.0:
    proc-log: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  promise-inflight@1.0.1:
    promise-inflight: private
  promise-retry@2.0.1:
    promise-retry: private
  promise@7.3.1:
    promise: private
  prop-types@15.8.1:
    prop-types: private
  property-information@6.5.0:
    property-information: private
  proxy-addr@2.0.7:
    proxy-addr: private
  pump@3.0.3:
    pump: private
  pumpify@1.5.1:
    pumpify: private
  punycode@2.3.1:
    punycode: private
  qs@6.13.0:
    qs: private
  quansync@0.2.10:
    quansync: private
  queue-microtask@1.2.3:
    queue-microtask: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  react-fast-compare@3.2.2:
    react-fast-compare: private
  react-is@16.13.1:
    react-is: private
  react-refresh@0.14.2:
    react-refresh: private
  react-router-dom@6.30.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-router-dom: private
  react-router@6.30.0(react@18.3.1):
    react-router: private
  react-transition-group@4.4.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-transition-group: private
  readable-stream@3.6.2:
    readable-stream: private
  readdirp@3.6.0:
    readdirp: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  regexpp@3.2.0:
    regexpp: private
  relay-runtime@12.0.0:
    relay-runtime: private
  remark-frontmatter@4.0.1:
    remark-frontmatter: private
  remark-mdx-frontmatter@1.1.1:
    remark-mdx-frontmatter: private
  remark-mdx@2.3.0:
    remark-mdx: private
  remark-parse@10.0.2:
    remark-parse: private
  remark-rehype@10.1.0:
    remark-rehype: private
  remedial@1.0.8:
    remedial: private
  remove-trailing-separator@1.1.0:
    remove-trailing-separator: private
  remove-trailing-spaces@1.0.9:
    remove-trailing-spaces: private
  require-directory@2.1.1:
    require-directory: private
  require-like@0.1.2:
    require-like: private
  requireindex@1.2.0:
    requireindex: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve.exports@2.0.3:
    resolve.exports: private
  resolve@1.22.10:
    resolve: private
  restore-cursor@3.1.0:
    restore-cursor: private
  retry@0.12.0:
    retry: private
  reusify@1.1.0:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  rimraf@3.0.2:
    rimraf: private
  rollup@4.44.2:
    rollup: private
  run-async@2.4.1:
    run-async: private
  run-parallel@1.2.0:
    run-parallel: private
  rxjs@7.8.2:
    rxjs: private
  sade@1.8.1:
    sade: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  scheduler@0.23.2:
    scheduler: private
  scuid@1.1.0:
    scuid: private
  semver@7.7.2:
    semver: private
  send@0.19.0:
    send: private
  sentence-case@3.0.4:
    sentence-case: private
  serve-static@1.16.2:
    serve-static: private
  set-cookie-parser@2.7.1:
    set-cookie-parser: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  setimmediate@1.0.5:
    setimmediate: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.3:
    shell-quote: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@3.0.7:
    signal-exit: private
  signedsource@1.0.0:
    signedsource: private
  slash@3.0.0:
    slash: private
  slice-ansi@3.0.0:
    slice-ansi: private
  snake-case@3.0.4:
    snake-case: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.7.4:
    source-map: private
  space-separated-tokens@2.0.2:
    space-separated-tokens: private
  spdx-correct@3.2.0:
    spdx-correct: private
  spdx-exceptions@2.5.0:
    spdx-exceptions: private
  spdx-expression-parse@3.0.1:
    spdx-expression-parse: private
  spdx-license-ids@3.0.21:
    spdx-license-ids: private
  sponge-case@1.0.1:
    sponge-case: private
  ssri@10.0.6:
    ssri: private
  stable-hash@0.0.5:
    stable-hash: private
  statuses@2.0.1:
    statuses: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  stream-shift@1.0.3:
    stream-shift: private
  stream-slice@0.1.2:
    stream-slice: private
  string-env-interpolation@1.0.1:
    string-env-interpolation: private
  string-hash@1.1.3:
    string-hash: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string.prototype.includes@2.0.1:
    string.prototype.includes: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.1.1:
    string_decoder: private
  stringify-entities@4.0.4:
    stringify-entities: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  style-to-object@0.4.4:
    style-to-object: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  swap-case@2.0.2:
    swap-case: private
  sync-fetch@0.6.0-2:
    sync-fetch: private
  tapable@2.2.2:
    tapable: private
  tar-fs@2.1.3:
    tar-fs: private
  tar-stream@2.2.0:
    tar-stream: private
  tar@7.4.3:
    tar: private
  text-table@0.2.0:
    text-table: private
  through2@2.0.5:
    through2: private
  through@2.3.8:
    through: private
  timeout-signal@2.0.0:
    timeout-signal: private
  tinyglobby@0.2.14:
    tinyglobby: private
  title-case@3.0.3:
    title-case: private
  tmp@0.0.33:
    tmp: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  toml@3.0.0:
    toml: private
  tr46@0.0.3:
    tr46: private
  trim-lines@3.0.1:
    trim-lines: private
  trough@2.2.0:
    trough: private
  ts-log@2.2.7:
    ts-log: private
  tsconfck@3.1.6(typescript@5.8.3):
    tsconfck: private
  tsconfig-paths@4.2.0:
    tsconfig-paths: private
  tslib@2.8.1:
    tslib: private
  tsutils@3.21.0(typescript@5.8.3):
    tsutils: private
  turbo-stream@2.4.1:
    turbo-stream: private
  type-check@0.4.0:
    type-check: private
  type-fest@4.41.0:
    type-fest: private
  type-is@1.6.18:
    type-is: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  ua-parser-js@1.0.40:
    ua-parser-js: private
  ufo@1.6.1:
    ufo: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  unc-path-regex@0.1.2:
    unc-path-regex: private
  undici-types@6.21.0:
    undici-types: private
  undici@6.21.3:
    undici: private
  unified@10.1.2:
    unified: private
  unique-filename@3.0.0:
    unique-filename: private
  unique-slug@4.0.0:
    unique-slug: private
  unist-util-generated@2.0.1:
    unist-util-generated: private
  unist-util-is@5.2.1:
    unist-util-is: private
  unist-util-position-from-estree@1.1.2:
    unist-util-position-from-estree: private
  unist-util-position@4.0.4:
    unist-util-position: private
  unist-util-remove-position@4.0.2:
    unist-util-remove-position: private
  unist-util-stringify-position@3.0.3:
    unist-util-stringify-position: private
  unist-util-visit-parents@5.1.3:
    unist-util-visit-parents: private
  unist-util-visit@4.1.2:
    unist-util-visit: private
  universalify@2.0.1:
    universalify: private
  unixify@1.0.0:
    unixify: private
  unpipe@1.0.0:
    unpipe: private
  unrs-resolver@1.10.1:
    unrs-resolver: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  upper-case-first@2.0.2:
    upper-case-first: private
  upper-case@2.0.2:
    upper-case: private
  uri-js@4.4.1:
    uri-js: private
  urlpattern-polyfill@10.1.0:
    urlpattern-polyfill: private
  util-deprecate@1.0.2:
    util-deprecate: private
  util@0.12.5:
    util: private
  utils-merge@1.0.1:
    utils-merge: private
  uuid@11.1.0:
    uuid: private
  uvu@0.5.6:
    uvu: private
  valibot@0.41.0(typescript@5.8.3):
    valibot: private
  validate-npm-package-license@3.0.4:
    validate-npm-package-license: private
  validate-npm-package-name@5.0.1:
    validate-npm-package-name: private
  value-or-promise@1.0.12:
    value-or-promise: private
  vary@1.1.2:
    vary: private
  vfile-message@3.1.4:
    vfile-message: private
  vfile@5.3.7:
    vfile: private
  vite-node@3.2.4(@types/node@22.16.0)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0):
    vite-node: private
  wcwidth@1.0.1:
    wcwidth: private
  web-encoding@1.1.5:
    web-encoding: private
  web-streams-polyfill@3.3.3:
    web-streams-polyfill: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  whatwg-mimetype@4.0.0:
    whatwg-mimetype: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@6.2.0:
    wrap-ansi: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  ws@7.5.10:
    ws: private
  xtend@4.0.2:
    xtend: private
  y18n@5.0.8:
    y18n: private
  yallist@4.0.0:
    yallist: private
  yaml-ast-parser@0.0.43:
    yaml-ast-parser: private
  yaml@2.8.0:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zwitch@2.0.4:
    zwitch: private
ignoredBuilds:
  - '@tailwindcss/oxide'
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.9.0
pendingBuilds: []
prunedAt: Sun, 06 Jul 2025 10:59:43 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/core@1.4.3'
  - '@emnapi/runtime@1.4.3'
  - '@emnapi/wasi-threads@1.0.2'
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.17.6'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.17.6'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.17.6'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-arm64@0.17.6'
  - '@esbuild/darwin-arm64@0.21.5'
  - '@esbuild/darwin-arm64@0.25.5'
  - '@esbuild/darwin-x64@0.17.6'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.17.6'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.17.6'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.17.6'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.17.6'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.17.6'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.17.6'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.17.6'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.17.6'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.17.6'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.17.6'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.17.6'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.17.6'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.17.6'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.17.6'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.17.6'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.17.6'
  - '@esbuild/win32-ia32@0.21.5'
  - '@esbuild/win32-ia32@0.25.5'
  - '@napi-rs/wasm-runtime@0.2.11'
  - '@parcel/watcher-android-arm64@2.5.1'
  - '@parcel/watcher-darwin-arm64@2.5.1'
  - '@parcel/watcher-darwin-x64@2.5.1'
  - '@parcel/watcher-freebsd-x64@2.5.1'
  - '@parcel/watcher-linux-arm-glibc@2.5.1'
  - '@parcel/watcher-linux-arm-musl@2.5.1'
  - '@parcel/watcher-linux-arm64-glibc@2.5.1'
  - '@parcel/watcher-linux-arm64-musl@2.5.1'
  - '@parcel/watcher-linux-x64-glibc@2.5.1'
  - '@parcel/watcher-linux-x64-musl@2.5.1'
  - '@parcel/watcher-win32-arm64@2.5.1'
  - '@parcel/watcher-win32-ia32@2.5.1'
  - '@rollup/rollup-android-arm-eabi@4.44.2'
  - '@rollup/rollup-android-arm64@4.44.2'
  - '@rollup/rollup-darwin-arm64@4.44.2'
  - '@rollup/rollup-darwin-x64@4.44.2'
  - '@rollup/rollup-freebsd-arm64@4.44.2'
  - '@rollup/rollup-freebsd-x64@4.44.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.44.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.44.2'
  - '@rollup/rollup-linux-arm64-gnu@4.44.2'
  - '@rollup/rollup-linux-arm64-musl@4.44.2'
  - '@rollup/rollup-linux-loongarch64-gnu@4.44.2'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.44.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.44.2'
  - '@rollup/rollup-linux-riscv64-musl@4.44.2'
  - '@rollup/rollup-linux-s390x-gnu@4.44.2'
  - '@rollup/rollup-linux-x64-gnu@4.44.2'
  - '@rollup/rollup-linux-x64-musl@4.44.2'
  - '@rollup/rollup-win32-arm64-msvc@4.44.2'
  - '@rollup/rollup-win32-ia32-msvc@4.44.2'
  - '@tailwindcss/oxide-android-arm64@4.1.11'
  - '@tailwindcss/oxide-darwin-arm64@4.1.11'
  - '@tailwindcss/oxide-darwin-x64@4.1.11'
  - '@tailwindcss/oxide-freebsd-x64@4.1.11'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.11'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.11'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.11'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.11'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.11'
  - '@tybys/wasm-util@0.9.0'
  - '@unrs/resolver-binding-android-arm-eabi@1.10.1'
  - '@unrs/resolver-binding-android-arm64@1.10.1'
  - '@unrs/resolver-binding-darwin-arm64@1.10.1'
  - '@unrs/resolver-binding-darwin-x64@1.10.1'
  - '@unrs/resolver-binding-freebsd-x64@1.10.1'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.10.1'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.10.1'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.10.1'
  - '@unrs/resolver-binding-linux-arm64-musl@1.10.1'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.10.1'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.10.1'
  - '@unrs/resolver-binding-linux-riscv64-musl@1.10.1'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.10.1'
  - '@unrs/resolver-binding-linux-x64-gnu@1.10.1'
  - '@unrs/resolver-binding-linux-x64-musl@1.10.1'
  - '@unrs/resolver-binding-wasm32-wasi@1.10.1'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.10.1'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.10.1'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
storeDir: J:\.pnpm-store\v10
virtualStoreDir: J:\Projects\EButler\enable\plugins\enable-loyalty\node_modules\.pnpm
virtualStoreDirMaxLength: 60
