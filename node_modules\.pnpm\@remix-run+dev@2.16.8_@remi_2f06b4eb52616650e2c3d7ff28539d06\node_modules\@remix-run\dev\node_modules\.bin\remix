#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06/node_modules/@remix-run/dev/dist/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06/node_modules/@remix-run/dev/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06/node_modules/@remix-run/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06/node_modules/@remix-run/dev/dist/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06/node_modules/@remix-run/dev/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06/node_modules/@remix-run/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../dist/cli.js" "$@"
else
  exec node  "$basedir/../../dist/cli.js" "$@"
fi
