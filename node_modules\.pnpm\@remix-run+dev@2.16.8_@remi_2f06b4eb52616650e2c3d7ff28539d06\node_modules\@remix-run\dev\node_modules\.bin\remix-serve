#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/@remix-run+serve@2.16.8_typescript@5.8.3/node_modules/@remix-run/serve/dist/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/@remix-run+serve@2.16.8_typescript@5.8.3/node_modules/@remix-run/serve/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/@remix-run+serve@2.16.8_typescript@5.8.3/node_modules/@remix-run/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/@remix-run+serve@2.16.8_typescript@5.8.3/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/@remix-run+serve@2.16.8_typescript@5.8.3/node_modules/@remix-run/serve/dist/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/@remix-run+serve@2.16.8_typescript@5.8.3/node_modules/@remix-run/serve/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/@remix-run+serve@2.16.8_typescript@5.8.3/node_modules/@remix-run/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/@remix-run+serve@2.16.8_typescript@5.8.3/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../@remix-run+serve@2.16.8_typescript@5.8.3/node_modules/@remix-run/serve/dist/cli.js" "$@"
else
  exec node  "$basedir/../../../../../../@remix-run+serve@2.16.8_typescript@5.8.3/node_modules/@remix-run/serve/dist/cli.js" "$@"
fi
