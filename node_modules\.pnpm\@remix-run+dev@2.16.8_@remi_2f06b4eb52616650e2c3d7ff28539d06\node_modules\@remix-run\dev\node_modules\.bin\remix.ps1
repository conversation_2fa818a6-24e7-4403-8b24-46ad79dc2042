#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="J:\Projects\EButler\enable\plugins\enable-loyalty\node_modules\.pnpm\@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06\node_modules\@remix-run\dev\dist\node_modules;J:\Projects\EButler\enable\plugins\enable-loyalty\node_modules\.pnpm\@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06\node_modules\@remix-run\dev\node_modules;J:\Projects\EButler\enable\plugins\enable-loyalty\node_modules\.pnpm\@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06\node_modules\@remix-run\node_modules;J:\Projects\EButler\enable\plugins\enable-loyalty\node_modules\.pnpm\@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06\node_modules;J:\Projects\EButler\enable\plugins\enable-loyalty\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06/node_modules/@remix-run/dev/dist/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06/node_modules/@remix-run/dev/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06/node_modules/@remix-run/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/@remix-run+dev@2.16.8_@remi_2f06b4eb52616650e2c3d7ff28539d06/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../../dist/cli.js" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../../dist/cli.js" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../../dist/cli.js" $args
  } else {
    & "node$exe"  "$basedir/../../dist/cli.js" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
