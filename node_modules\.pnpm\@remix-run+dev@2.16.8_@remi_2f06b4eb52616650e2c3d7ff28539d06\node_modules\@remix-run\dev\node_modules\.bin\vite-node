#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/vite-node@3.2.4_@types+node_f285c8d298c8175276b3a894bc5b4ee3/node_modules/vite-node/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/vite-node@3.2.4_@types+node_f285c8d298c8175276b3a894bc5b4ee3/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/vite-node@3.2.4_@types+node_f285c8d298c8175276b3a894bc5b4ee3/node_modules/vite-node/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/vite-node@3.2.4_@types+node_f285c8d298c8175276b3a894bc5b4ee3/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../vite-node@3.2.4_@types+node_f285c8d298c8175276b3a894bc5b4ee3/node_modules/vite-node/vite-node.mjs" "$@"
else
  exec node  "$basedir/../../../../../../vite-node@3.2.4_@types+node_f285c8d298c8175276b3a894bc5b4ee3/node_modules/vite-node/vite-node.mjs" "$@"
fi
