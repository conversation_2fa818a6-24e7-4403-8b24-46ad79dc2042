'use strict';

var hmr = require('./chunk-hmr.cjs');
require('node:events');
require('picocolors');
require('debug');
require('./utils.cjs');
require('node:url');
require('node:module');
require('node:fs');
require('pathe');



exports.createHmrEmitter = hmr.createHmrEmitter;
exports.createHotContext = hmr.createHotContext;
exports.getCache = hmr.getCache;
exports.handleMessage = hmr.handleMessage;
exports.reload = hmr.reload;
exports.sendMessageBuffer = hmr.sendMessageBuffer;
exports.viteNodeHmrPlugin = hmr.viteNodeHmrPlugin;
