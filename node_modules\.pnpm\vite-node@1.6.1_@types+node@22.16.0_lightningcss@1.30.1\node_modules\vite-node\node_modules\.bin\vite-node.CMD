@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=J:\Projects\EButler\enable\plugins\enable-loyalty\node_modules\.pnpm\vite-node@1.6.1_@types+node@22.16.0_lightningcss@1.30.1\node_modules\vite-node\node_modules;J:\Projects\EButler\enable\plugins\enable-loyalty\node_modules\.pnpm\vite-node@1.6.1_@types+node@22.16.0_lightningcss@1.30.1\node_modules;J:\Projects\EButler\enable\plugins\enable-loyalty\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=J:\Projects\EButler\enable\plugins\enable-loyalty\node_modules\.pnpm\vite-node@1.6.1_@types+node@22.16.0_lightningcss@1.30.1\node_modules\vite-node\node_modules;J:\Projects\EButler\enable\plugins\enable-loyalty\node_modules\.pnpm\vite-node@1.6.1_@types+node@22.16.0_lightningcss@1.30.1\node_modules;J:\Projects\EButler\enable\plugins\enable-loyalty\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\vite-node.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\vite-node.mjs" %*
)
