#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/vite@5.4.19_@types+node@22.16.0_lightningcss@1.30.1/node_modules/vite/bin/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/vite@5.4.19_@types+node@22.16.0_lightningcss@1.30.1/node_modules/vite/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/vite@5.4.19_@types+node@22.16.0_lightningcss@1.30.1/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/vite@5.4.19_@types+node@22.16.0_lightningcss@1.30.1/node_modules/vite/bin/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/vite@5.4.19_@types+node@22.16.0_lightningcss@1.30.1/node_modules/vite/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/vite@5.4.19_@types+node@22.16.0_lightningcss@1.30.1/node_modules:/mnt/j/Projects/EButler/enable/plugins/enable-loyalty/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/vite.js" "$@"
else
  exec node  "$basedir/../../bin/vite.js" "$@"
fi
